# 风险单元模态框 NFC 编号编辑丢失 Bug 修复日志

> 相关源码文件与文档引用：
>
> - 主要修复文件 unitModal.tsx：[src/pages/doubleGuard/modal/unit/unitModal.tsx](../../src/pages/doubleGuard/modal/unit/unitModal.tsx)
> - 相关枚举定义 CheckMethod：[src/types/doubleGuard.ts](../../src/types/doubleGuard.ts)

---

## 一、Bug 描述与现象

### 问题现象
在风险单元编辑模态框中，当检查方式为 NFC（checkMethod: 3）时，编辑已有数据时 NFC 编号字段（nfcNumber）无法正确回显，表现为：
- 新增模式：NFC 编号输入正常
- 编辑模式：NFC 编号字段为空，无法显示原有数据

### 用户影响
- 用户无法看到已保存的 NFC 编号
- 编辑时需要重新输入 NFC 编号
- 可能导致数据丢失或重复输入

---

## 二、Bug 调试过程

### Debug Log 分析

通过在关键位置添加 console.log，追踪数据流：

```typescript
// 1. 原始数据检查
console.log("渲染时 checkMethod:", data?.data?.checkMethod);
console.log("CheckMethod.Nfc:", CheckMethod.Nfc);
console.log("条件判断结果:", data?.data?.checkMethod === CheckMethod.Nfc);
console.log("当前 nfcNumber 值:", data?.data?.nfcNumber);

// 2. 表单设置过程追踪
console.log("原始数据:", formValues);
console.log("checkMethod:", formValues.checkMethod, "nfcNumber:", formValues.nfcNumber);

// 3. 表单设置后验证
setTimeout(() => {
  const currentValues = getFormApiRef.current.getValues();
  console.log("设置后的表单值:", currentValues);
  console.log("表单中的 checkMethod:", currentValues.checkMethod);
  console.log("表单中的 nfcNumber:", currentValues.nfcNumber);
}, 100);
```

### 关键发现

通过 Debug Log 发现了问题的根本原因：

1. **原始数据正常**：`checkMethod: 3, nfcNumber: "nfccode2"`
2. **设置的表单值正常**：表单设置时包含正确的 nfcNumber
3. **设置后获取异常**：`表单中的 nfcNumber: undefined`

这表明问题出现在 `setValues` 之后，表单没有正确保存 `nfcNumber` 的值。

---

## 三、Bug 根本原因分析

### React 组件生命周期问题

问题的核心在于 **React 组件渲染时序** 和 **条件渲染** 的冲突：

1. **初始状态**：模态框打开时，`checkMethod` 还未设置，NFC 输入框未渲染
2. **数据设置**：`setValues` 同时设置 `checkMethod: 3` 和 `nfcNumber: "nfccode2"`
3. **渲染更新**：React 重新渲染，此时 `checkMethod` 已更新，NFC 输入框开始渲染
4. **字段丢失**：由于 NFC 输入框在 `setValues` 时不存在，`nfcNumber` 字段被忽略

### 条件渲染导致的时序问题

```typescript
// 问题代码：条件渲染导致字段不存在
{formApi.getValue("checkMethod") === CheckMethod.Nfc && (
  <Form.Input
    field="nfcNumber"  // 这个字段在 setValues 时可能不存在
    label="NFC编号"
    rules={rules}
  />
)}
```

当 `setValues` 执行时：
- `checkMethod` 从 `undefined` 变为 `3`
- 但 NFC 输入框还未渲染，`nfcNumber` 字段不存在
- Semi Design Form 忽略不存在的字段，导致 `nfcNumber` 丢失

---

## 四、解决方案设计

### 方案选择

考虑了多种解决方案：

1. **延迟设置方案**：先设置 `checkMethod`，再延迟设置 `nfcNumber`
2. **分步设置方案**：分两次调用 `setValues`
3. **监听补偿方案**：使用 `useEffect` 监听并补偿丢失的字段

最终采用 **双重保障方案**：延迟设置 + 监听补偿

### 实现细节

```typescript
// 1. 立即延迟设置（第一重保障）
getFormApiRef.current.setValues(formValues);

if (formValues.checkMethod === CheckMethod.Nfc && formValues.nfcNumber) {
  setTimeout(() => {
    console.log("延迟设置 nfcNumber:", formValues.nfcNumber);
    getFormApiRef.current.setValue("nfcNumber", formValues.nfcNumber);
  }, 50);
}

// 2. useEffect 监听补偿（第二重保障）
useEffect(() => {
  if (
    getFormApiRef.current &&
    data?.data?.checkMethod === CheckMethod.Nfc &&
    data?.data?.nfcNumber
  ) {
    const currentNfcNumber = getFormApiRef.current.getValue("nfcNumber");
    if (!currentNfcNumber) {
      console.log("补充设置 nfcNumber:", data.data.nfcNumber);
      getFormApiRef.current.setValue("nfcNumber", data.data.nfcNumber);
    }
  }
}, [data?.data?.checkMethod, data?.data?.nfcNumber, getFormApiRef]);
```

---

## 五、修复验证

### 验证步骤

1. **编辑模式测试**：打开已有 NFC 数据的风险单元编辑
2. **控制台检查**：观察 Debug Log 输出
3. **字段回显**：确认 NFC 编号正确显示
4. **保存测试**：确认编辑保存功能正常

### 预期结果

- 控制台显示：`延迟设置 nfcNumber: nfccode2`
- 可能显示：`补充设置 nfcNumber: nfccode2`（如果第一次设置失败）
- NFC 编号字段正确回显原有数据
- 编辑和保存功能正常

---

## 六、技术总结

### 关键技术点

1. **React 条件渲染时序**：条件渲染的组件在条件满足前不存在于 DOM
2. **Semi Design Form 字段管理**：表单只管理已渲染的字段
3. **异步状态更新**：`setValues` 和组件重渲染是异步过程
4. **防御性编程**：使用多重保障确保数据完整性

### 最佳实践

1. **避免条件渲染字段丢失**：对于动态字段，考虑使用 `display: none` 而非条件渲染
2. **分步设置复杂表单**：先设置控制字段，再设置依赖字段
3. **使用 useEffect 监听补偿**：确保数据完整性
4. **充分的 Debug Log**：帮助快速定位问题

---

## 七、任务时间与耗时分析

| 阶段/子任务        | 开始时间             | 结束时间             | 耗时     | 主要内容/备注                      | 主要错误/异常              |
| ------------------ | -------------------- | -------------------- | -------- | ---------------------------------- | -------------------------- |
| 问题复现与分析     | 2025-07-30 14:00     | 2025-07-30 14:15     | 15min    | 复现 Bug，初步分析原因             | 初始怀疑数据源问题         |
| Debug Log 添加     | 2025-07-30 14:15     | 2025-07-30 14:30     | 15min    | 添加关键位置的调试日志             | 日志位置不够全面           |
| 根本原因定位       | 2025-07-30 14:30     | 2025-07-30 14:45     | 15min    | 通过日志分析定位时序问题           | 理解 React 渲染时序        |
| 解决方案设计       | 2025-07-30 14:45     | 2025-07-30 15:00     | 15min    | 设计双重保障方案                   | 方案选择和权衡             |
| 代码实现与测试     | 2025-07-30 15:00     | 2025-07-30 15:15     | 15min    | 实现延迟设置和监听补偿             | setTimeout 时间调优        |
| 验证与文档编写     | 2025-07-30 15:15     | 2025-07-30 15:30     | 15min    | 功能验证和开发日志编写             | 文档结构和内容组织         |
| **总计**           | **2025-07-30 14:00** | **2025-07-30 15:30** | **1.5h** |                                    |                            |

---

## 八、开发总结与经验

### 核心收获

1. **React 条件渲染陷阱**：条件渲染的表单字段在条件不满足时不存在，会导致数据丢失
2. **表单数据完整性**：复杂表单的数据设置需要考虑字段依赖关系和渲染时序
3. **Debug 驱动开发**：充分的调试日志是快速定位问题的关键
4. **防御性编程思维**：使用多重保障机制确保关键功能的可靠性

### 迁移建议

- 类似的条件渲染表单字段问题可能在其他模态框中存在
- 建议统一检查所有动态表单字段的数据设置逻辑
- 考虑抽象通用的表单数据设置工具函数

---

## 九、用户 prompt 备忘录（时间序列，自动归纳版）

1. 用户报告风险单元编辑模态框中 NFC 编号字段无法正确回显的问题
2. 要求分析和修复编辑模式下 NFC 编号丢失的 Bug
3. 用户提供了具体的测试场景和期望行为
4. 要求参照已有开发日志格式编写 Bug 修复日志
5. 强调需要详细的 Debug Log 分析和 React 组件生命周期问题解释

> 注：本列表为自动归纳，覆盖了本次 NFC 编号 Bug 修复的关键用户指令和需求。

---

## 十、用户 prompt 明细原文（时间序列，完整收录）

1. 仿照 @`/Users/<USER>/Workspace/vren/chogori/docs/dev-log/20250621-AlarmIndexContent.md` 写一篇关于此bug修复的开发日志，特别是其中debug log的写法和bug原因解释（涉及到react组件生命周期），今天是20250730

> 注：本次为单一 Bug 修复任务，用户 prompt 相对简洁，主要聚焦于问题解决和文档记录。
