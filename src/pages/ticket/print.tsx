import {
  IconChevronDown,
  IconChevronUp,
  IconMinus,
  IconMinusCircleStroked,
  IconPlus,
  IconRedo,
  IconUndo,
} from "@douyinfe/semi-icons";
import {
  Checkbox,
  CheckboxGroup,
  Collapsible,
  Descriptions,
  Dropdown,
  Empty,
  Input,
  InputGroup,
  InputNumber,
  Radio,
  RadioGroup,
  Slider,
  Spin,
  Toast,
} from "@douyinfe/semi-ui";
import {
  getJobSlicePrint,
  getJsTemplateUser,
  getPrintTemplate,
  updatePrintTemplate,
} from "api";
import classNames from "classnames";
import { CopyModal, copyModalAtom } from "components";
import { base_url } from "config";
import dayjs from "dayjs";
import { useAtom } from "jotai";
import { get, set } from "lodash-es";
import { isEmpty, isNil, isNotNil } from "ramda";
import React, { useMemo } from "react";
import ReactGridLayout from "react-grid-layout";
import { useResizeDetector } from "react-resize-detector";
import { useNavigate, useParams } from "react-router-dom";
import { HelpCircleFilledIcon } from "tdesign-icons-react";
import { useCopyToClipboard } from "usehooks-ts";
import {
  PrintExportModal,
  printExportModalAtom,
} from "./modal/printExportModal";

import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import "react-grid-layout/css/styles.css";
import "./print.less";

const Sizes = {
  A4: {
    // A4 纸大小 210mm*297mm
    width: Math.round(210 / (25.4 / 96)),
    height: Math.round(297 / (25.4 / 96)),
  },
};

function parseTemplate(template) {
  if (typeof template === "string") {
    template = JSON.parse(template);
    if (Array.isArray(template)) {
      return {
        width: Sizes.A4.width,
        height: Sizes.A4.height,
        layouts: template,
      };
    }
    if (Array.isArray(template?.layouts)) {
      return template;
    }
  }
  return {
    width: Sizes.A4.width,
    height: Sizes.A4.height,
    layouts: [],
  };
}

type Callback = (path: string, value: any) => void;

function isObject(obj: any) {
  return Object.prototype.toString.call(obj) === "[object Object]";
}

function objectWalk<T>(obj: T, fn: Callback) {
  function walk(_obj: string | number | boolean | T, path: string) {
    Object.keys(_obj).forEach((k: string) => {
      const _path = path ? `${path}.${k}` : k;
      if (isObject(_obj[k] as T)) {
        walk(_obj[k], _path);
      } else if (Array.isArray(_obj[k])) {
        (_obj[k] as any[]).forEach((_v, _i) => {
          objectWalk(_v, (p, v) => {
            fn(`${_path}.${_i}.${p}`, v);
          });
        });
      } else {
        fn(_path, _obj[k]);
      }
    });
  }
  if (isObject(obj)) {
    walk(obj, "");
  } else if (Array.isArray(obj)) {
    obj.forEach((_obj, i) => {
      walk(_obj, `${i}`);
    });
  }
}

/**
 * 根据 data 将 lyts(ReactGridLayouts 的 layouts) 中的动态内容替换为实际值，并返回新的 lyts
 *
 * 举例：
 * 1. 将单元格向下展开，如 <|a.b.[]|> 意思是指 a.b 是数组，将 a.b 的每个值展开渲染为一个单元格填充到 lyts 中
 * 2. 将 <|a.b|> 替换为 data.a.b 的值
 *
 * defaultValue 举例
 * 1. 如果单元格变量 item.content 是 a.b.[]，每一项是字符串，可以设置 defaultValue 为一个 JSON 数组字符串，如 '["默认值1", "默认值2"]'
 * 2. 如果单元格变量 item.content 是 a.b.[].c.d，可以设置 defaultValue 为一个 JSON 对象数组字符串，如 '[{"a": {"b":"默认值1"}},{"a": {"b":"默认值2"}}]'
 * 3. 如果单元格变量 item.content 是 a.b，可以设置 defaultValue 为一个任意值，如 '默认值1'

  单测 1. 展开数组元素（`isExpand` 为 `true`）
  ```javascript
  describe("replaceDynamicContents - 展开数组元素", () => {
    it("应该正确展开数组并替换为单元格", () => {
      const lyts = {
        layouts: [
          {
            content: "<|a.b.[]|>", // 动态内容，代表 a.b 是一个数组，应该展开成多个单元格
            type: "CellItem",
            isExpand: true,
            label: "动态单元格",
            labelStyle: { display: "block", height: "40px" },
            contentStyle: { height: "30px" }
          }
        ]
      };

      const data = {
        a: {
          b: ["值1", "值2"]
        }
      };

      const result = replaceDynamicContents(lyts, data);

      // 期望的结果：每个数组元素变成一个单元格
      expect(result.layouts).toEqual([
        {
          content: "值1",
          type: "TextItem",
          style: { display: "block", height: "40px" },
          itemStyle: undefined,
          x: undefined, y: undefined, w: undefined, h: undefined, i: expect.any(String)
        },
        {
          content: "值2",
          type: "TextItem",
          style: { display: "block", height: "40px" },
          itemStyle: undefined,
          x: undefined, y: undefined, w: undefined, h: undefined, i: expect.any(String)
        }
      ]);
    });
  });
  ```

  单测 2. 使用默认值
  ```javascript
  describe("replaceDynamicContents - 使用默认值", () => {
    it("当数据路径不存在时，应该使用默认值", () => {
      const lyts = {
        layouts: [
          {
            content: "<|a.b|>", // a.b 不存在，应该使用默认值
            type: "CellItem",
            label: "动态单元格",
            labelStyle: { display: "block", height: "40px" },
            contentStyle: { height: "30px" },
            defaultValue: "默认值"
          }
        ]
      };

      const data = {}; // a.b 不存在

      const result = replaceDynamicContents(lyts, data);

      expect(result.layouts).toEqual([
        {
          content: "默认值", // 使用默认值
          type: "TextItem",
          style: { display: "block", height: "40px" },
          itemStyle: undefined,
          x: undefined, y: undefined, w: undefined, h: undefined, i: expect.any(String)
        }
      ]);
    });
  });
  ```

  单测 3. 使用时间格式化（`timeFormat`）
  ```javascript
  describe("replaceDynamicContents - 时间格式化", () => {
    it("应该正确格式化时间", () => {
      const lyts = {
        layouts: [
          {
            content: "<|a.b|>", // 动态内容，替换为时间
            type: "CellItem",
            label: "日期单元格",
            labelStyle: { display: "block", height: "40px" },
            contentStyle: { height: "30px" },
            timeFormat: "YYYY-MM-DD"
          }
        ]
      };

      const data = {
        a: {
          b: "2023-10-01T12:00:00Z" // 时间字符串
        }
      };

      const result = replaceDynamicContents(lyts, data);

      // 期望的输出格式为 "YYYY-MM-DD"
      expect(result.layouts).toEqual([
        {
          content: "2023-10-01", // 格式化后的时间
          type: "TextItem",
          style: { display: "block", height: "40px" },
          itemStyle: undefined,
          x: undefined, y: undefined, w: undefined, h: undefined, i: expect.any(String)
        }
      ]);
    });
  });
  ```

 * @param lyts 布局
 * @param data 数据
 */
function replaceDynamicContents(lyts, data) {
  if (typeof console.group === "function")
    console.group("解析字符串内容为 JSON");
  objectWalk(data, (path, value) => {
    if (typeof value === "string" && value.length) {
      try {
        value = JSON.parse(value);
        set(data, path, value);
        console.log(`[${path}] 可以被解析为 json`);
      } catch (e) {
        // ignore
      }
    }
  });
  if (typeof console.groupEnd === "function") console.groupEnd();
  console.log(
    `打印作业票信息：
数据：`,
    data,
    `
布局：`
  );
  console.table(lyts.layouts.slice());
  if (typeof console.group === "function") console.group("替换动态内容");
  lyts.layouts = lyts.layouts.reduce((newLyts, item) => {
    if (
      typeof item.content === "string" &&
      /<\|([^|]+)\|>/g.test(item.content) // 存在 <|abc|>  类型的动态内容
    ) {
      const {
        timeFormat, // 时间格式化
        isExpand, // 是否展开数组
        isCheckbox, // 展示为多选样式
        isRadio, // 展示为单选样式
        onlyShowChecked, // 只展示选中的
        selectedValuePath, // 选中的值路径
        isImage, // 是否展示为图片
        valueTemplate, // 读取候选项值时使用的变量模板
        valueMapping = [], // 候选项值映射
        defaultValue, // 默认值
      } = item;
      if (item.type === "CellItem") {
        // 元素是动态单元格，可能会从自身往下生成多个单元格
        const variables = [...item.content.matchAll(/<\|([^|]+)\|>/g)]?.map(
          (r) => r?.[1]
        );
        const key = variables?.[0];
        // 向下展开自动填充，如安全措施
        // isExpand=true 时， CellItem 转换为纵向的多个 TextItem(或 ImageItem)
        if (isExpand === true) {
          if (key.includes(".[]")) {
            // 如 <|a.b.[].c.d|> 是数组向下展开
            const paths = key.split(".[]").filter((str) => str.length);
            let nextPath = paths.shift();
            let arr = get(data, nextPath); // 读取 a.b 的值
            if (!Array.isArray(arr) || isEmpty(arr) || isNil(arr)) {
              if (typeof defaultValue === "string" && defaultValue.length) {
                try {
                  arr = JSON.parse(defaultValue);
                  if (!Array.isArray(arr)) {
                    console.error(
                      `检查一下 [${item.label}] 单元格的默认值 [${defaultValue}] 不是 JSON 数组，是不是写错了？`
                    );
                    return newLyts;
                  }
                } catch (e) {
                  console.error(
                    `检查一下 [${item.label}] 单元格的默认值 [${defaultValue}] 不是 JSON 数组，是不是写错了？`
                  );
                  return newLyts;
                }
              } else {
                console.error(
                  `检查一下 [${item.label}] 单元格的内容路径 [${key}] 是不是写错了？[${nextPath}] 不是数组`,
                  data
                );
                return newLyts;
              }
            }
            while (paths.length) {
              // 读取 a.b 数组每一项中的 c.d 的值，拼接到 arr 中
              nextPath = paths.shift();
              if (nextPath.startsWith(".")) {
                nextPath = nextPath.slice(1);
              }
              arr = arr
                .map((d) => {
                  console.log(`读取 [${key}] 的 [${nextPath}]`); // 例：读取 a.b.[].c.d 的 c, while 的下一次读 d
                  let vInside = get(d, nextPath);
                  if (vInside === undefined) {
                    console.error(
                      `[${nextPath}] 值不存在，检查路径是不是写错了？`,
                      d
                    );
                  }
                  return vInside;
                })
                .flat();
            }
            const newType = isImage ? "ImageItem" : "TextItem";
            if (Array.isArray(arr)) {
              if (item.labelStyle.display !== "none") {
                // 第一个单元格，可以理解为表头
                newLyts.push({
                  x: item.x,
                  y: item.y,
                  w: item.w,
                  h: parseFloat(item.labelStyle.height) || item.h,
                  i: item.i,
                  type: "TextItem",
                  style: item.labelStyle,
                  content: item.label,
                });
              }
              arr.forEach((content, i) => {
                let newContent;
                if (
                  // 把 valueTemplate 里的变量替换成实际值
                  typeof valueTemplate === "string" &&
                  Object.prototype.toString.call(content) === "[object Object]"
                ) {
                  newContent = valueTemplate.replace(
                    /<\|([^|]+)\|>/g,
                    (_, key) => {
                      console.log(`读取 [${key}]`);
                      let vInside = get(content, key);
                      if (vInside === undefined) {
                        console.error(
                          `[${item.label}] 单元格 [${key}] 值不存在，检查路径是不是写错了？`,
                          content
                        );
                        vInside = "";
                      }
                      if (Array.isArray(valueMapping)) {
                        const displayValue = valueMapping.find(
                          (display) => display.key == vInside
                        );
                        if (displayValue) {
                          vInside = displayValue.value;
                        }
                      }
                      if (isNotNil(timeFormat)) {
                        if (dayjs(vInside, timeFormat).isValid()) {
                          return dayjs(vInside).format(timeFormat);
                        }
                      }
                      return vInside;
                    }
                  );
                  if (isCheckbox || isRadio || isImage) {
                    newContent = newContent.split(",");
                  }
                } else {
                  if (Array.isArray(valueMapping)) {
                    const displayValue = valueMapping.find(
                      (display) => display.key == content
                    );
                    if (displayValue) {
                      newContent = displayValue.value;
                    }
                  }
                  if (isNotNil(timeFormat)) {
                    newContent = dayjs(content).format(timeFormat);
                  }
                }
                newLyts.push({
                  x: item.x,
                  y: item.y,
                  w: item.w,
                  h: parseFloat(item.contentStyle.height) || item.h,
                  i: `${item.i}-${i}`,
                  type: newType,
                  style: item.contentStyle,
                  itemStyle: item.itemStyle,
                  content: newContent || content || defaultValue,
                  isCheckbox,
                  isRadio,
                });
              });
            }
          } else {
            console.error(
              `[${item.label}] 单元格的 isExpand 是 true, 但是 content [${item.content}] 中的变量不包含 .[]，检查路径是不是写错了？`,
              item,
              data
            );
          }
        } else {
          // isExpand=false 时，CellItem 的内容是自己内部横向展开的
          let arr;
          if (key.includes(".[]")) {
            const paths = key.split(".[]").filter((str) => str.length);
            let nextPath = paths.shift();
            arr = get(data, nextPath);
            if (!Array.isArray(arr) || isEmpty(arr) || isNil(arr)) {
              if (typeof defaultValue === "string" && defaultValue.length) {
                try {
                  arr = JSON.parse(defaultValue);
                } catch (e) {
                  arr = undefined;
                }
              } else {
                console.error(
                  `检查一下 [${item.label}] 单元格的内容路径 [${key}] 是不是写错了？[${nextPath}] 不是数组`,
                  data
                );
                return newLyts;
              }
            }
            while (paths.length) {
              nextPath = paths.shift();
              if (nextPath.startsWith(".")) {
                nextPath = nextPath.slice(1);
              }
              arr = arr.map((d) => {
                console.log(`读取 [${key}] 的 [${nextPath}]`);
                let vInside = get(d, nextPath);
                if (vInside === undefined) {
                  console.error(
                    `[${nextPath}] 值不存在，检查路径是不是写错了？`,
                    d
                  );
                }
                return vInside;
              });
            }
          } else if (variables?.length === 1) {
            console.log(`读取 [${key}]`);
            arr = get(data, key);
            if (isEmpty(arr) || isNil(arr)) {
              if (typeof defaultValue === "string" && defaultValue.length) {
                try {
                  arr = JSON.parse(defaultValue);
                } catch (e) {
                  arr = undefined;
                }
              } else {
                console.error(
                  `[${item.label}] 单元格 [${key}] 值[${arr}]被认为不存在`,
                  item,
                  data
                );
              }
            }
          }
          if (Array.isArray(arr)) {
            if (isCheckbox || isRadio) {
              // checkbox 和 radio 单元格，需要从 data 中根据 selectedValuePath 读取选中项
              if (typeof selectedValuePath === "string") {
                console.log(`读取 [${selectedValuePath}]`);
                let selectedValue = get(data, selectedValuePath);
                if (selectedValue !== undefined) {
                  if (!Array.isArray(selectedValue)) {
                    selectedValue = [selectedValue];
                  }
                } else {
                  console.error(
                    `[${selectedValuePath}] 值不存在，检查路径是不是写错了？`,
                    data
                  );
                  selectedValue = [];
                }
                item.selectedValue = selectedValue;
                if (onlyShowChecked && Array.isArray(selectedValue)) {
                  // 只显示选中项时，过滤掉未选中的
                  arr = arr.filter((_) => {
                    if (_?.value !== undefined) {
                      return selectedValue.includes(_.value);
                    }
                    return selectedValue.includes(_);
                  });
                }
              }
            }
            item.content = arr.map((v) => {
              // 将 item.content 替换为实际值
              if (
                typeof valueTemplate === "string" &&
                Object.prototype.toString.call(v) === "[object Object]"
              ) {
                v = valueTemplate.replace(/<\|([^|]+)\|>/g, (_, key) => {
                  console.log(`读取 [${key}]`);
                  let vInside = get(v, key);
                  if (vInside === undefined) {
                    console.error(
                      `[${item.label}] 单元格 [${key}] 值不存在，检查路径是不是写错了？`,
                      v
                    );
                    vInside = "";
                  }
                  if (Array.isArray(valueMapping)) {
                    const displayValue = valueMapping.find(
                      (display) => display.key == vInside
                    );
                    if (displayValue) {
                      vInside = displayValue.value;
                    }
                  }
                  if (isNotNil(timeFormat)) {
                    if (dayjs(vInside, timeFormat).isValid()) {
                      return dayjs(vInside).format(timeFormat);
                    }
                  }
                  return vInside;
                });
                if (isCheckbox || isRadio || isImage) {
                  v = v.split(","); // 转数组
                }
                return v;
              }
              if (v?.label && v?.value) {
                return {
                  label: v.label,
                  value: v.value,
                };
              }
              if (v?.label && v?.id) {
                return {
                  label: v.label,
                  value: v.id,
                };
              }
              let label = v;
              if (Array.isArray(valueMapping)) {
                const displayValue = valueMapping.find(
                  (display) => display.key == v
                );
                if (displayValue) {
                  label = displayValue.value;
                }
              }
              return {
                label,
                value: v,
              };
            });
            newLyts.push(item);
          } else {
            item.content = item.content.replace(/<\|([^|]+)\|>/g, (_, key) => {
              console.log(`读取 [${key}]`);
              let v = get(data, key);
              if (isEmpty(v) || isNil(v)) {
                console.error(
                  `[${item.label}] 单元格 [${key}] 值[${v}]被认为不存在`,
                  item,
                  data
                );
                v = defaultValue ?? "";
              }
              if (Array.isArray(valueMapping)) {
                const displayValue = valueMapping.find(
                  (display) => display.key == v
                );
                if (displayValue) {
                  v = displayValue.value;
                }
              }
              if (isNotNil(timeFormat)) {
                if (dayjs(v, timeFormat).isValid()) {
                  return dayjs(v).format(timeFormat);
                }
              }
              return v;
            });
            newLyts.push(item);
          }
        }
      } else {
        // 元素是单一的，动态文本或纯文本
        item.content = item.content.replace(/<\|([^|]+)\|>/g, (_, key) => {
          console.log(`读取 [${key}]`);
          let v = get(data, key);
          if (isEmpty(v) || isNil(v)) {
            console.error(
              `[${item.label}] 单元格 [${key}] 值[${v}]被认为不存在，将使用[${defaultValue ?? ""}]`,
              item,
              data
            );
            v = defaultValue ?? "";
          }
          if (Array.isArray(valueMapping)) {
            const displayValue = valueMapping.find(
              (display) => display.key == v
            );
            if (displayValue) {
              v = displayValue.value;
            }
          }
          if (isNotNil(timeFormat)) {
            if (dayjs(v, timeFormat).isValid()) {
              return dayjs(v).format(timeFormat);
            }
          }
          return v;
        });
        newLyts.push(item);
      }
    } else {
      newLyts.push(item);
    }
    return newLyts;
  }, []);
  if (typeof console.groupEnd === "function") console.groupEnd();
  return lyts;
}

const ElementNames = {
  CellItem: "单元格",
  ImageItem: "图片",
  TextItem: "文本",
};
const Elements = {
  CellItem,
  ImageItem,
  TextItem,
  PageBreaker,
};

function getId(lyts) {
  return `${Math.max(...lyts.map((item) => +item.i).concat(0)) + 1}`;
}
function insertNewLayout(lyts, type, width) {
  const ele = {
    i: getId(lyts),
    x: 0,
    y: 0,
    w: width / 3,
    h: 40,
    type,
    content: "",
  };
  if (type === "TextItem") {
    ele.style = {
      width: "100%",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      borderWidth: "1px 0 0 1px",
      borderStyle: "solid",
      borderColor: "#dadada",
    };
  }
  if (type === "CellItem") {
    ele.label = "标题";
    ele.labelStyle = {
      padding: "0 10px",
      borderWidth: "1px 0 0 1px",
      borderStyle: "solid",
      borderColor: "#dadada",
    };
    ele.content = "值";
    ele.contentStyle = {
      padding: "0 10px",
      borderWidth: "1px 0 0 1px",
      borderStyle: "solid",
      borderColor: "#dadada",
    };
    ele.isArray = false;
    ele.isExpand = false;
    ele.isCheckbox = false;
    ele.isRadio = false;
  }
  return lyts.concat(ele);
}

const Context = React.createContext({});

function useHistory(defaultItem) {
  const [history, setHistory] = React.useState({
    cursor: 0,
    data: [defaultItem],
  });

  const onChange = React.useCallback((newRecordOrFn) => {
    if (typeof newRecordOrFn === "function") {
      setHistory((_) => {
        const newData = newRecordOrFn(_.data[_.cursor]);
        const data = _.data.slice(0, _.cursor + 1);
        data.push(newData);
        return {
          cursor: _.cursor + 1,
          data,
        };
      });
    } else {
      setHistory((_) => {
        const data = _.data.slice(0, _.cursor + 1);
        data.push(newRecordOrFn);
        return {
          cursor: _.cursor + 1,
          data,
        };
      });
    }
  }, []);

  const onUndo = React.useCallback(() => {
    setHistory((_) => {
      if (history.cursor > 0) {
        return {
          cursor: _.cursor - 1,
          data: _.data,
        };
      }
      return _;
    });
  }, []);

  const onRedo = React.useCallback(() => {
    setHistory((_) => {
      if (history.cursor < history.data.length - 1) {
        return {
          cursor: _.cursor + 1,
          data: _.data,
        };
      }
      return _;
    });
  }, []);

  return {
    current: history.data[history.cursor] || [],
    onChange,
    onUndo,
    onRedo,
  };
}

function useLayouts(template, templateStr, isEditing = true) {
  const {
    current: layouts,
    onChange: setLayouts,
    onUndo,
    onRedo,
  } = useHistory(template.layouts);
  const [{ width, height }, setSize] = React.useState({
    width: template.width,
    height: template.height,
  });
  const onLayoutChange = React.useCallback((lyts) => {
    setLayouts((_) => {
      const newLyts = lyts.map((lyt) => {
        const newLyt = {
          ...(_.find((item) => item.i === lyt.i) || {}),
          ...lyt,
        };
        return newLyt;
      });
      return newLyts;
    });
  }, []);
  const [scale, setScale] = React.useState(
    isEditing ? 1 : window.innerWidth / template.width
  );
  const onResize = React.useCallback(
    (_width) => {
      setScale(_width / width);
    },
    [width]
  );
  const resizer = useResizeDetector({
    onResize,
  });

  const [current, setCurrent] = React.useState(
    template.layouts.length > 0 ? template.layouts[0].i : null
  );
  const onDragStart = React.useCallback((arr, node) => {
    setCurrent(node?.i);
  }, []);

  const layoutStyle = React.useMemo(
    () => ({
      width: width,
      minHeight: height,
      transformOrigin: "top left",
      transform: scale !== 1 ? `scale3d(${scale}, ${scale}, 1)` : "",
    }),
    [scale, width, height]
  );

  React.useEffect(() => {
    setSize({ width: template.width, height: template.height });
  }, [template.width, template.height]);

  React.useEffect(() => {
    setLayouts(template.layouts);
  }, [template.layouts]);

  React.useEffect(() => {
    if (layouts.length > 0 && !current) {
      setCurrent(layouts[0].i);
    }
  }, [layouts, current]);

  React.useLayoutEffect(() => {
    setScale(window.innerWidth / template.width);
  }, [template, width, height]);

  return {
    width,
    height,
    setSize,
    isEditing,
    resizeRef: resizer.ref,
    layouts,
    setLayouts,
    onUndo,
    onRedo,
    onLayoutChange,
    scale,
    setScale,
    onResize,
    current,
    setCurrent,
    onDragStart,
    layoutStyle,
    templateStr,
  };
}

function SettingSidebar() {
  const { current, layouts } = React.useContext(Context);

  if (!current || !layouts.some((item) => item.i === current)) {
    return null;
  }
  return (
    <div className="w-1/4 h-full overflow-auto bg-gray-200 p-4 flex flex-col gap-3 justify-start">
      <ElementSetting />
    </div>
  );
}

const defaultStyleKeys = [
  "width",
  "height",
  "fontSize",
  "fontWeight",
  "fontStyle",
  "fontFamily",
  "textDecoration",
  "textAlign",
  "whiteSpace",
  "color",
  "background",
  "borderWidth",
  "borderStyle",
  "borderColor",
  "borderRadius",
  "padding",
  "display",
  "gap",
  "flexDirection",
  "flexWrap",
  "alignContent",
  "alignItems",
  "justifyContent",
  "justifyItems",
  "alignSelf",
  "justifySelf",
  "justifyContent",
  "lineHeight",
];
const cssAttributeNames = {
  width: "宽度",
  height: "高度",
  fontSize: "字体大小",
  fontWeight: "字体粗细",
  fontStyle: "字体风格",
  fontFamily: "字体",
  textDecoration: "文本装饰",
  textAlign: "文本对齐",
  color: "颜色",
  background: "背景色",
  borderWidth: "边框宽度",
  borderStyle: "边框样式",
  borderColor: "边框颜色",
  borderRadius: "边框圆角",
  padding: "内边距",
  display: "显示方式",
  lineHeight: "行高",
};

function ElementSetting() {
  const { width, height, setSize, current, layouts, setLayouts } =
    React.useContext(Context);
  let idx;
  const _ = layouts.find((item, i) => {
    if (item.i === current) {
      idx = i;
      return true;
    }
    return false;
  });
  const {
    i,
    type,
    label,
    labelStyle,
    contentStyle,
    content,
    style,
    timeFormat,
    isExpand,
    itemStyle,
    isCheckbox,
    isRadio,
    onlyShowChecked,
    separator,
    selectedValuePath,
    isImage,
    valueTemplate,
    valueMapping,
    defaultValue,
  } = _;
  const setCurrent = (lyt) =>
    setLayouts((lyts) => {
      lyts = lyts.slice();
      lyts[idx] = lyt;
      return lyts;
    });
  const isCellItem = type === "CellItem";

  return (
    <div className="flex flex-col gap-3">
      <h3>画布尺寸</h3>
      <Descriptions
        data={[
          {
            key: "宽",
            value: (
              <InputNumber
                value={width}
                onChange={(e) => {
                  if (typeof e === "number") {
                    setSize({ height, width: e });
                  }
                }}
              />
            ),
          },
          {
            key: "最小高度",
            value: (
              <InputNumber
                value={height}
                onChange={(e) => {
                  if (typeof e === "number") {
                    setSize({ width, height: e });
                  }
                }}
              />
            ),
          },
        ]}
      />
      <h3>
        设置{i}({type})控件
      </h3>
      <ItemStyle item={_} onChange={(k, v) => setCurrent({ ..._, [k]: v })} />
      <div className="flex flex-col items-start justify-between gap-3">
        <h4>
          内容
          <br />
          <span className="text-[12px] text-[#666]">
            使用 <code>{`<|路径.变量名|>`}</code> 尖括号引用 JSON 变量
          </span>
          {isCellItem && (
            <span className="text-[12px] text-[#666]">
              使用 <code>{`<|路径.[].变量名|>`}</code> 方括号引用数组变量
            </span>
          )}
        </h4>
        <Input
          value={content}
          onChange={(e) => setCurrent({ ..._, content: e })}
        />
      </div>
      <div className="flex flex-col items-start justify-between gap-3">
        <h4>
          默认值
          <br />
          <span className="text-[12px] text-[#666]">
            当引用的变量不存在或为空时，显示此默认值
          </span>
          {isCellItem && isExpand && (
            <span className="text-[12px] text-[#666]">
              <br />
              当引用数组变量时，默认值需要是有效的JSON数组格式
            </span>
          )}
        </h4>
        <Input
          value={defaultValue}
          onChange={(e) => setCurrent({ ..._, defaultValue: e })}
        />
      </div>
      <div className="flex flex-col items-start justify-between gap-3">
        <h4>
          timeFormat
          <br />
          <span className="text-[12px] text-[#666]">
            当值是时间时，可以使用字符串模板格式化，如 `YYYY年MM月DD日`
          </span>
        </h4>
        <Input
          value={timeFormat}
          onChange={(e) => setCurrent({ ..._, timeFormat: e })}
        />
      </div>
      <CustomCollapse header="控件样式">
        <StyleSetting
          style={style}
          onChange={(k, v) =>
            setCurrent({ ..._, style: { ..._.style, [k]: v } })
          }
        />
      </CustomCollapse>
      {isCellItem ? (
        <>
          <div className="flex flex-col items-start justify-between gap-3">
            <h4>单元格标题</h4>
            <Input
              value={label}
              onChange={(e) => setCurrent({ ..._, label: e })}
            />
          </div>
          <CustomCollapse header="Label 样式">
            <StyleSetting
              style={labelStyle}
              onChange={(k, v) =>
                setCurrent({ ..._, labelStyle: { ..._.labelStyle, [k]: v } })
              }
            />
          </CustomCollapse>
          <CustomCollapse header="Content 样式">
            <StyleSetting
              style={contentStyle}
              onChange={(k, v) =>
                setCurrent({
                  ..._,
                  contentStyle: { ..._.contentStyle, [k]: v },
                })
              }
            />
          </CustomCollapse>
          <Checkbox
            checked={isExpand}
            onChange={(e) =>
              setCurrent({
                ..._,
                isExpand: e.target.checked,
                labelStyle: {
                  ..._.labelStyle,
                  width: "100%",
                  height: `${_.h}px`,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                },
                contentStyle: {
                  ..._.contentStyle,
                  width: "100%",
                  height: `${_.h + 20}px`,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                },
              })
            }
          >
            当值是数组时
            <br />
            （content变量内包含'[]'路径），每一个值作为单元格向下延展
          </Checkbox>
          <CustomCollapse header="每一个值的样式">
            <StyleSetting
              style={itemStyle}
              onChange={(k, v) =>
                setCurrent({ ..._, itemStyle: { ..._.itemStyle, [k]: v } })
              }
            />
          </CustomCollapse>
          <Checkbox
            checked={isCheckbox}
            onChange={(e) =>
              setCurrent({
                ..._,
                isCheckbox: e.target.checked,
                isRadio: false,
                contentStyle: {
                  ..._.contentStyle,
                  alignItems: "start",
                  padding: "10px",
                },
              })
            }
          >
            值作为复选框样式
          </Checkbox>
          <Checkbox
            checked={isRadio}
            onChange={(e) =>
              setCurrent({
                ..._,
                isRadio: e.target.checked,
                isCheckbox: false,
                contentStyle: {
                  ..._.contentStyle,
                  alignItems: "start",
                  padding: "10px",
                },
              })
            }
          >
            值作为单选框样式
          </Checkbox>
          {isCheckbox || isRadio ? (
            <>
              <Checkbox
                checked={onlyShowChecked}
                onChange={(e) =>
                  setCurrent({ ..._, onlyShowChecked: e.target.checked })
                }
              >
                只显示选中项
              </Checkbox>
              <div className="flex flex-col items-start justify-between gap-3">
                <h4>选中项的JSON路径(不需要{`<||>`}包裹)</h4>
                <Input
                  value={selectedValuePath}
                  onChange={(e) => setCurrent({ ..._, selectedValuePath: e })}
                />
              </div>
            </>
          ) : null}
          <Checkbox
            checked={isImage}
            onChange={(e) => setCurrent({ ..._, isImage: e.target.checked })}
          >
            值是图片
          </Checkbox>
          <div className="flex flex-col items-start justify-between gap-3">
            <h4>
              当值是数组并展示在同一个单元格内时（比如动火方式），用什么连接符号拼接
            </h4>
            <Input
              value={separator}
              onChange={(e) => setCurrent({ ..._, separator: e })}
            />
          </div>
          <div className="flex flex-col items-start justify-between gap-3">
            <h4>
              当值是 Object 时使用字符串模板展示多个内容（比如动火人及作业编号）
            </h4>
            <Input
              value={valueTemplate}
              onChange={(e) => setCurrent({ ..._, valueTemplate: e })}
            />
          </div>
          <CustomCollapse header="设定显示值">
            <button
              className="btn btn-sm btn-primary"
              onClick={() => {
                const cur = valueMapping || [];
                setCurrent({
                  ..._,
                  valueMapping: [
                    ...cur,
                    { key: `值${cur.length}`, value: `显示值${cur.length}` },
                  ],
                });
              }}
            >
              <IconPlus />
              添加
            </button>
            {(valueMapping || []).map(({ key, value }, index) => (
              <div
                key={`${key}${index}`}
                className="flex items-center gap-2 my-2"
              >
                <InputGroup className="flex flex-nowrap gap-2">
                  <Input
                    placeholder="值"
                    value={key}
                    onChange={(v) => {
                      const newValueMapping = valueMapping.slice();
                      newValueMapping[index].key = v;
                      setCurrent({ ..._, valueMapping: newValueMapping });
                    }}
                  />
                  <span>=</span>
                  <Input
                    placeholder="显示值"
                    value={value}
                    onChange={(v) => {
                      const newValueMapping = valueMapping.slice();
                      newValueMapping[index].value = v;
                      setCurrent({ ..._, valueMapping: newValueMapping });
                    }}
                  />
                </InputGroup>
                <IconMinusCircleStroked
                  onClick={() => {
                    const newValueMapping = valueMapping.slice();
                    newValueMapping.splice(index, 1);
                    setCurrent({ ..._, valueMapping: newValueMapping });
                  }}
                />
              </div>
            ))}
          </CustomCollapse>
        </>
      ) : null}
    </div>
  );
}

function CustomCollapse({ header, children }) {
  const [open, toggleOpen] = React.useState(false);
  return (
    <>
      <h4
        onClick={() => toggleOpen((_) => !_)}
        className="flex items-center justify-start gap-2 text-sm cursor-pointer"
      >
        {header}
        {open ? <IconChevronUp /> : <IconChevronDown />}
      </h4>
      <Collapsible isOpen={open}>{children}</Collapsible>
    </>
  );
}

const positions = [
  { key: "x", name: "距离左侧" },
  { key: "y", name: "距离顶部" },
  { key: "w", name: "宽度" },
  { key: "h", name: "高度" },
];

function ItemStyle({ item, onChange }) {
  const { width } = React.useContext(Context);
  return (
    <Descriptions
      data={positions
        .map(({ key, name }) => ({
          key: name,
          value: (
            <InputNumber
              value={item[key]}
              onChange={(e) => {
                if (typeof e === "number") {
                  onChange(key, e);
                }
              }}
            />
          ),
        }))
        .concat([
          {
            key: "距离右侧",
            value: <InputNumber value={width - item.x - item.w} disabled />,
          },
        ])}
    />
  );
}

function camelToDash(str) {
  return str.replace(/([A-Z])/g, function (match) {
    return "-" + match.toLowerCase();
  });
}

function StyleSetting({ style = {}, onChange }) {
  const attributes = Array.from(
    new Set(defaultStyleKeys.concat(Object.keys(style)))
  );
  return (
    <Descriptions
      data={attributes.map((k) => ({
        key: (
          <>
            {k}
            <HelpCircleFilledIcon
              className="cursor-pointer"
              onClick={() =>
                window.open(
                  `https://developer.mozilla.org/zh-CN/docs/Web/CSS/${camelToDash(k)}`,
                  "_blank"
                )
              }
            />
            {cssAttributeNames[k] ? (
              <>
                <br />
                {cssAttributeNames[k]}
              </>
            ) : (
              ""
            )}
          </>
        ),
        value: <Input value={style[k]} onChange={(e) => onChange(k, e)} />,
      }))}
    />
  );
}

export function PrintConfigPage() {
  const params = useParams();
  const { isLoading, data, refetch } = useQuery({
    queryKey: ["print_config", params?.id],
    queryFn: () => {
      return getPrintTemplate(params?.id);
    },
  });
  const { data: jobTemplateRes } = useQuery({
    queryKey: ["getJobTemplate", params?.id],
    queryFn: () => getJsTemplateUser(params?.id),
  });
  const jobTemplate = useMemo(() => {
    return jobTemplateRes?.data ?? {};
  }, [jobTemplateRes]);

  const lyts = React.useMemo(() => {
    return parseTemplate(data?.data?.printTemplate || "[]");
  }, [isLoading, data?.data?.printTemplate]);

  const ctx = useLayouts(lyts, data?.data?.printTemplate);

  return (
    <>
      <div className="navbar-start">
        <a className="btn btn-ghost text-xl">
          {jobTemplate?.category?.name}打印配置
        </a>
      </div>
      <Context.Provider value={ctx}>
        <PrintExportModal />
        <div className="w-full h-full flex">
          <div
            ref={ctx.resizeRef}
            className="flex-1 h-full flex flex-col gap-3"
          >
            <LayoutTool />
            {ctx.layouts.length ? (
              <Layouts className="outline-1 outline-dashed outline-[#666]" />
            ) : (
              <Empty
                className="h-full justify-center"
                image={
                  isLoading ? (
                    <Spin />
                  ) : (
                    <IllustrationNoContent
                      style={{ width: 150, height: 150 }}
                    />
                  )
                }
                description={isLoading ? "加载中..." : "暂无内容，请添加"}
              />
            )}
          </div>
          <SettingSidebar />
        </div>
      </Context.Provider>
    </>
  );
}

export function PrintPreviewPage() {
  const params = useParams();
  const [counter, setCounter] = React.useState(0);

  const jobQuery = useQuery({
    queryKey: ["getJobSlicePrint", params?.id],
    queryFn: () => {
      return getJobSlicePrint(params?.id).then((res) => {
        setCounter((_) => _ + 1);
        return res;
      });
    },
    enabled: Boolean(params?.id),
  });

  const printTemplateQuery = useQuery({
    queryKey: [
      "print_config",
      jobQuery?.data?.data?.jobSliceInfo?.template?.id,
    ],
    queryFn: () => {
      return getPrintTemplate(jobQuery.data.data.jobSliceInfo.template.id).then(
        (res) => {
          setCounter((_) => _ + 1);
          return res;
        }
      );
    },
    enabled: Boolean(jobQuery?.data?.data?.jobSliceInfo?.template?.id),
  });

  const lyts = React.useMemo(() => {
    return replaceDynamicContents(
      parseTemplate(printTemplateQuery?.data?.data?.printTemplate || "[]"),
      jobQuery?.data?.data
    );
  }, [jobQuery.isLoading, printTemplateQuery.isLoading, counter]);

  const ctx = useLayouts(lyts, printTemplateQuery?.data?.printTemplate, false);

  return (
    <Context.Provider value={ctx}>
      <div className="print-el">
        <div className="flex justify-between items-center gap-3 print-ignore">
          <div className="flex items-center gap-2">
            <Scaler className="!w-[300px]" />
          </div>
        </div>
        <div className="print-el" ref={ctx.resizeRef}>
          <Layouts className="print-el-transform" />
        </div>
      </div>
    </Context.Provider>
  );
}

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error, info) {
    console.error(error, info.componentStack);
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return this.props.fallback;
    }

    return this.props.children;
  }
}

function Layouts({ className = "" }) {
  const ctx = React.useContext(Context);
  window.ctx = ctx;
  const {
    width,
    isEditing,
    layouts,
    onLayoutChange,
    scale,
    current,
    onDragStart,
    layoutStyle,
  } = ctx;

  return (
    <div className="overflow-auto p-[2px] print-el">
      <div className={`relative ${className}`} style={layoutStyle}>
        {/* {pageNumCounter} */}
        <ReactGridLayout
          className="min-h-full print-layout"
          useCSSTransforms={false} // css transform 无法被 html2pdf
          transformScale={scale}
          // 开重叠
          allowOverlap={false}
          // 引力方向
          compactclassName="btn btn-sm btn-rounded btn-vertical"
          // 开引力挤兑
          preventCollision={false}
          verticalCompact
          width={width}
          cols={width}
          colWidth={1}
          rowHeight={1}
          containerPadding={[0, 0]}
          margin={[0, 0]}
          isResizable={isEditing}
          isDraggable={isEditing}
          layouts={layouts}
          onDragStart={isEditing ? onDragStart : undefined}
          onLayoutChange={onLayoutChange}
        >
          {layouts.map((item) => {
            const Item = Elements[item.type];
            if (!Item) return null;

            return (
              <div
                key={item.i}
                data-grid={item}
                className={classNames(
                  "flex overflow-hidden",
                  isEditing &&
                    "group hover:outline-[color:var(--semi-color-primary)] outline-dashed outline-1 outline-[transparent]",
                  current === item.i &&
                    "!outline-[color:var(--semi-color-primary)]"
                )}
              >
                <ErrorBoundary>
                  <Item {...item} />
                </ErrorBoundary>
              </div>
            );
          })}
        </ReactGridLayout>
      </div>
    </div>
  );
}

function LayoutTool() {
  const {
    width,
    height,
    layouts,
    setLayouts,
    current,
    setCurrent,
    templateStr,
  } = React.useContext(Context);
  const navigate = useNavigate();
  const params = useParams();
  const [copiedText, copy] = useCopyToClipboard();
  const [printExport, setPrintExport] = useAtom(printExportModalAtom);
  const [copyModal, setCopyModal] = useAtom(copyModalAtom);
  const queryClient = useQueryClient();
  const mutation = useMutation({
    mutationFn: updatePrintTemplate,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: ["printTemplatePage"] });
      Toast.success(opts);
      // navigate(SpecialWorkRoutes.JS_TEMPLATE_USER);
    },
  });

  const onClear = () => {
    setLayouts([]);
  };

  const onSave = () => {
    mutation.mutate({
      values: {
        printTemplate: JSON.stringify({
          width,
          height,
          layouts,
        }),
      },
      id: params.id,
    });
  };

  const onExport = () => {
    console.debug(templateStr);
    if (navigator.clipboard) {
      copy(templateStr ?? "")
        .then(() => {
          Toast.success("导出成功，模板已复制至您的粘贴板");
        })
        .catch((error) => {
          console.error("Failed to copy!", error);
        });
    } else {
      setCopyModal({
        visible: true,
        content: templateStr,
      });
    }
  };

  const onImport = () => {
    console.debug("导入");
    setPrintExport({
      visible: true,
      id: params?.id,
      data: templateStr ?? null,
    });
  };

  return (
    <div className="flex gap-4 items-center justify-between flex-wrap">
      <div className="flex gap-4 items-center">
        <CopyModal />
        <button className="btn btn-sm btn-rounded btn-primary" onClick={onSave}>
          保存
        </button>
        <button
          className="btn btn-sm btn-rounded btn-primary"
          onClick={onClear}
        >
          清空
        </button>
        <button
          className="btn btn-sm btn-rounded btn-primary"
          onClick={onImport}
        >
          导入
        </button>
        <button
          className="btn btn-sm btn-rounded btn-primary"
          onClick={onExport}
        >
          导出
        </button>
        {["CellItem", "ImageItem", "TextItem"].map((type) => (
          <button
            className="btn btn-rounded btn-sm btn-accent"
            onClick={() =>
              setLayouts((_) => {
                const newLyts = insertNewLayout(_, type, width);
                if (!_.length) {
                  setCurrent(newLyts[0].i);
                }
                return newLyts;
              })
            }
            key={type}
          >
            {ElementNames[type]}
          </button>
        ))}
        {current && (
          <>
            <Copy />
            <Delete />
          </>
        )}
      </div>
      <div className="flex items-center pr-2 gap-1">
        <UndoRedo />
        <Scaler />
      </div>
    </div>
  );
}

function UndoRedo() {
  const { onUndo, onRedo } = React.useContext(Context);

  return (
    <>
      <div
        onClick={() => onUndo()}
        className={classNames(
          "text-[#8a8a8a] text-[16px] whitespace-nowrap flex items-center",
          "cursor-pointer hover:text-[#5b5e69]"
        )}
      >
        <IconUndo />
        撤销
      </div>
      <div
        onClick={() => onRedo()}
        className={classNames(
          "text-[#8a8a8a] text-[16px] whitespace-nowrap flex items-center",
          "cursor-pointer hover:text-[#5b5e69]"
        )}
      >
        <IconRedo />
        重做
      </div>
    </>
  );
}

function getScaleRatio(n) {
  return Math.min(4, Math.max(0.1, n / 100));
}

function getPrecision(number, e = 6) {
  let t = 1;
  for (; e > 0; t *= 10, e--); // eslint-disable-line
  for (; e < 0; t /= 10, e++); // eslint-disable-line
  return Math.round(number * t) / t;
}

function Scaler({ className }) {
  const { onUndo, onRedo, layouts, scale, setScale } =
    React.useContext(Context);

  if (!layouts.length) {
    return null;
  }

  return (
    <>
      <IconMinus
        onClick={() => setScale((_) => getScaleRatio(_ * 100 - 1))}
        className={classNames(
          "text-[#8a8a8a] text-[14px]",
          "cursor-pointer hover:text-[#5b5e69]"
        )}
      />
      <Slider
        className={classNames("w-[88px]", className)}
        onChange={(_) => setScale(getScaleRatio(_))}
        value={scale * 100}
        step={1}
        min={10}
        max={400}
      />
      <IconPlus
        onClick={() => setScale((_) => getScaleRatio(_ * 100 + 1))}
        className={classNames(
          "text-[#8a8a8a] text-[14px]",
          "cursor-pointer hover:text-[#5b5e69]"
        )}
      />
      <Dropdown
        trigger="click"
        render={
          <Dropdown.Menu>
            <Dropdown.Item onClick={() => setScale(0.5)} key="50">
              50%
            </Dropdown.Item>
            <Dropdown.Item onClick={() => setScale(1)} key="100">
              100%
            </Dropdown.Item>
            <Dropdown.Item onClick={() => setScale(2)} key="200">
              200%
            </Dropdown.Item>
          </Dropdown.Menu>
        }
      >
        <span className="text-[12px] cursor-pointer w-[50px] flex items-center justify-between">
          {getPrecision(scale * 100, 0)}%
          <IconChevronDown />
        </span>
      </Dropdown>
    </>
  );
}

function Copy() {
  const { current, layouts, setLayouts } = React.useContext(Context);
  const item = layouts.find((item) => item.i === current);
  if (!item) {
    return null;
  }

  return (
    <button
      className="btn btn-rounded btn-sm btn-accent"
      onClick={(e) => {
        setLayouts((_) => {
          return _.slice().concat({
            ...item,
            i: getId(_),
          });
        });
      }}
    >
      复制选中元素
    </button>
  );
}

function Delete() {
  const { layouts, setLayouts, current, setCurrent } =
    React.useContext(Context);
  const item = layouts.find((item) => item.i === current);
  if (!item) {
    return null;
  }

  return (
    <button
      className="btn btn-rounded btn-sm btn-accent"
      onClick={(e) => {
        setLayouts((_) => {
          return _.filter((i) => i.i !== item.i);
        });
        if (item.i === current) {
          setCurrent(null);
        }
      }}
    >
      删除选中元素
    </button>
  );
}

/**
 在 replaceDynamicContents 中按需将 CellItem 转换为其他形式
 1. isCheckbox=true 时，content 部分渲染为 Checkbox
 1. isRadio=true 时，content 部分渲染为 Radio
 1. isExpand=true 时， CellItem 根据 content 渲染为多个纵向 TextItem （isImage=true时渲染为ImageItem）
 */
function CellItem({
  label,
  labelStyle,
  content,
  contentStyle,
  style,
  isExpand,
  itemStyle,
  isCheckbox,
  isRadio,
  isImage,
  onlyShowChecked,
  separator = "",
  selectedValue,
}) {
  if (isExpand) {
    // 编辑状态下渲染 CellItem 的纵向扩充表示
    return (
      <div
        style={style}
        className={classNames("w-full h-full flex items-center", "flex-col")}
      >
        <div
          style={{
            ...labelStyle,
            borderWidth: "0 0 1px 0",
          }}
          className={classNames("flex items-center truncate", "w-full")}
        >
          {label}
        </div>
        <div
          style={contentStyle}
          className={classNames("flex items-center truncate", "w-full")}
        >
          {isCheckbox ? (
            <Checkbox checked />
          ) : isRadio ? (
            <Radio checked />
          ) : isImage ? (
            <ImageItem content={content} style={itemStyle} />
          ) : null}
          {!isImage ? content : null}
        </div>
      </div>
    );
  }
  return (
    <div style={style} className="w-full h-full flex items-center">
      <div
        style={labelStyle}
        className="flex items-center h-full w-[fit-content]"
      >
        {label}
      </div>
      <div
        style={contentStyle}
        className="flex items-center truncate h-full flex-1"
      >
        {isCheckbox && Array.isArray(content) ? (
          <CheckboxGroup
            direction="horizontal"
            value={
              !onlyShowChecked && Array.isArray(selectedValue)
                ? selectedValue
                : content.map((v) => v.value)
            }
            options={content}
          />
        ) : isRadio && Array.isArray(content) ? (
          <RadioGroup
            direction="horizontal"
            value={
              !onlyShowChecked && Array.isArray(selectedValue)
                ? selectedValue[0]
                : content[0].value
            } // ???
            options={content}
          />
        ) : isImage ? (
          Array.isArray(content) ? (
            content.map((src, idx) => (
              <ImageItem
                key={`${src?.value || src}-${idx}`}
                content={src?.value || src}
                style={itemStyle}
              />
            ))
          ) : (
            <ImageItem content={content} style={itemStyle} />
          )
        ) : Array.isArray(content) && content?.[0]?.value !== undefined ? (
          content.map((c) => c.label).join(separator)
        ) : Array.isArray(content) && separator?.length ? (
          content.join(separator)
        ) : (
          content
        )}
      </div>
    </div>
  );
}

function ImageItem({ content, style, itemStyle }) {
  if (!content?.length) {
    return null;
  }
  if (Array.isArray(content)) {
    return (
      <div style={style}>
        {content.map((src) => {
          src = src?.startsWith?.("http") ? src : `${base_url}${src}`;
          return (
            <img
              key={src}
              style={
                itemStyle || {
                  width: style?.width,
                  height: style?.height,
                }
              }
              src={src}
            />
          );
        })}
      </div>
    );
  }
  const src = content?.startsWith?.("http") ? content : `${base_url}${content}`;
  return (
    <div style={style}>
      <img
        style={
          itemStyle || {
            width: style?.width,
            height: style?.height,
          }
        }
        src={src}
      />
    </div>
  );
}

function TextItem({ content, style }) {
  return <p style={style} dangerouslySetInnerHTML={{ __html: content }} />;
}

function PageBreaker({ breaker }) {
  return (
    <div
      className="ignore-print w-full h-[0px] border-t border-[#666] border-dashed"
      style={{ marginTop: breaker }}
    />
  );
}
