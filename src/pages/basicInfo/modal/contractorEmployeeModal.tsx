import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useCallback, useEffect, useRef } from "react";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { contractorEmployeeApis } from "api/basicInfo";
import { contractorEmployeeAtoms } from "atoms";
import { CONTRACTOR_STATUS_MAP, ContractorSearch, Upload } from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { EnhancedUpload } from "components/upload-enhanced";
import { useAtom } from "jotai";
import { omit } from "ramda";
import { useNavigate } from "react-router-dom";
import { filterEditData } from "utils";

export const ContractorEmployeeModal = () => {
  const operation = "Edit";
  const newTitle = "新增承包商人员信息";
  const editTitle = "编辑承包商人员信息";
  const ruleMessage = ["此为必填项!"];

  const atoms = contractorEmployeeAtoms;
  const apis = contractorEmployeeApis;

  const entity = atoms.entity;
  const uniqueKey = `${entity}${operation}`;

  const [editModalAtom, setEditModalAtom] = useAtom(atoms.editModal);
  const [fnAtom] = useAtom(atoms.Fn);

  const title = editModalAtom?.id ? editTitle : newTitle;
  const rules = [{ required: true, message: ruleMessage[0] }];

  const queryClient = useQueryClient();
  const queryKey = "list" + entity;

  const getFormApiRef = useRef<any>(null);
  const navigate = useNavigate();

  const { isLoading, data } = useQuery({
    queryKey: [`${entity}-${editModalAtom?.id ?? ""}`],
    queryFn: () => {
      if (editModalAtom?.id) {
        return apis.get(editModalAtom?.id);
      }
    },
    enabled: !!editModalAtom?.id,
  });
  const mutation = useMutation({
    mutationFn: editModalAtom?.id ? apis.update : apis.create,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: ["list" + entity] });
        fnAtom?.refetch?.();
        destroyDraft(uniqueKey);
        getFormApiRef?.current?.reset?.();
        setEditModalAtom({
          id: "",
          show: false,
        });
      }
    },
  });

  // 自动填充表单
  useEffect(() => {
    if (editModalAtom?.id && data?.data?.name && getFormApiRef.current) {
      const items = omit([], data?.data);
      const filteredData = filterEditData(items);

      // 清理可能的数据污染 - 确保图片字段数据正确
      const cleanedData = {
        ...filteredData,
        // 确保图片字段数据来源正确，但不覆盖 _upload 字段
        idCardImageList: items.idCardImageList || [],
        insuranceImageList: items.insuranceImageList || [],
        // 保留 filterEditData 创建的 _upload 字段
        // idCardImageList_upload 和 insuranceImageList_upload 已经由 filterEditData 正确处理
      };

      console.log(
        "ContractorEmployeeModal: Setting form values with cleaned data:",
        cleanedData
      );

      getFormApiRef.current.setValues(cleanedData, { isOverride: true });
    } else {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
    }
  }, [editModalAtom?.id, data, getFormApiRef]);

  useEffect(() => {
    return () => {
      getFormApiRef?.current?.reset?.();
      getFormApiRef.current?.setValues?.({}, { isOverride: true });
      destroyDraft(uniqueKey);
    };
  }, []);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft(`${entity}${operation}`);
    setEditModalAtom({
      id: "",
      show: false,
    });
  }, [setEditModalAtom, mutation]);

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        let obj = {
          ...values,
          //user-defined code here
        };
        if (editModalAtom?.id) {
          mutation.mutate({
            id: editModalAtom?.id,
            values: obj,
          });
        } else {
          mutation.mutate(obj);
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  return (
    <>
      <DraftTrigger id={uniqueKey} draftAtom={editModalAtom} />
      <Modal
        title={title}
        visible={editModalAtom?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        keepDOM
        width={800}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          <Row gutter={20}>
            <Col span={12}>
              <ContractorSearch
                field="contractorId"
                label="承包商"
                placeholder="选择承包商"
                className="w-full"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="name"
                label="姓名"
                trigger="blur"
                rules={rules}
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Input
                field="mobile"
                label="手机号码"
                trigger="blur"
                rules={rules}
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="idNumber"
                label="身份证号"
                trigger="blur"
                rules={rules}
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.Select
                field="status"
                label="状态"
                className="w-full"
                rules={rules}
              >
                {CONTRACTOR_STATUS_MAP.map((item) => (
                  <Form.Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Form.Select.Option>
                ))}
              </Form.Select>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Form.TextArea field="comment" label="备注" trigger="blur" />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              {/* 图像(.jpg，.png，.gif，.jpeg)，文档(.doc，.docx，.pdf，.xlsx，.xls，.ppt)   大小限制在50M */}
              {/* <EnhancedUpload */}
              <Upload
                label="身份证照片"
                formField="idCardImageList"
                field="idCardImageList_upload"
                arrayProcessType="array"
                type="img"
                listType="picture"
                accept=".jpg,.png,.gif,.jpeg"
                maxSize={51200} //KB
                multiple={true}
              />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              {/* 图像(.jpg，.png，.gif，.jpeg)，文档(.doc，.docx，.pdf，.xlsx，.xls，.ppt)   大小限制在50M */}
              {/* <EnhancedUpload */}
              <Upload
                label="安责险照片"
                formField="insuranceImageList"
                field="insuranceImageList_upload"
                arrayProcessType="array"
                type="img"
                listType="picture"
                accept=".jpg,.png,.gif,.jpeg"
                maxSize={51200} //KB
                multiple={true}
              />
            </Col>
          </Row>
          {editModalAtom?.id ? null : (
            <Draft id={uniqueKey} draftAtom={atoms.editModal} />
          )}
        </Form>
      </Modal>
    </>
  );
};
