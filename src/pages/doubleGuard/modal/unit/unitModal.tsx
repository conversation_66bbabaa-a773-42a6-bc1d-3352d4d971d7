import { unitEditModalAtom, unitFnAtom } from "@atoms/doubleGuard";
import { Avatar, Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery } from "@tanstack/react-query";
import { createRiskUnit, getRiskUnit, updateRiskUnit } from "api/doubleGuard";
import { CheckMethod } from "atoms/doubleGuard";
import {
  ALLOW_ALLOWNOT_MAP,
  DepartmentSearch,
  EmployeeSearch,
  ObjectSearch,
  SAFETY_SIGNS_MAP,
  SafetySigns,
  Upload,
} from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { base_url } from "config";
import { useAtom } from "jotai";
import { useCallback, useEffect, useRef } from "react";

export const UnitModal = () => {
  const getFormApiRef = useRef<any>(null);
  const [unitEdit, setUnitEdit] = useAtom(unitEditModalAtom);
  const [unitFn] = useAtom(unitFnAtom);

  const { isLoading, data } = useQuery({
    queryKey: [`unitRisk-${unitEdit?.id ?? ""}`],
    queryFn: () => {
      if (unitEdit?.id) {
        return getRiskUnit(unitEdit?.id);
      }
    },
    enabled: !!unitEdit?.id,
  });
  const mutation = useMutation({
    mutationFn: unitEdit?.id ? updateRiskUnit : createRiskUnit,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        unitFn?.refetch?.();
        destroyDraft("unitEdit");
        setUnitEdit({
          id: "",
          show: false,
        });
      }
    },
  });

  useEffect(() => {
    if (unitEdit?.id && data?.data?.name && getFormApiRef.current) {
      const item = data?.data;
      console.log("原始数据:", item);
      console.log(
        "checkMethod:",
        item.checkMethod,
        "nfcNumber:",
        item.nfcNumber
      );

      const formValues = {
        ...item,
        riskObjectId: item?.riskObject?.id ?? null,
        areaId: item?.area?.id ?? null,
        liableDepartmentId: parseInt(item?.liableDepartment?.id ?? 0),
        liablePersonId: item?.liablePerson?.id ?? null,
        safetySigns: item.safetySigns ? JSON.parse(item.safetySigns) : [],
        // 确保 checkMethod 是数字类型
        checkMethod: parseInt(item.checkMethod) || item.checkMethod,
      };

      console.log("设置的表单值:", formValues);
      console.log("表单中的 nfcNumber:", formValues.nfcNumber);

      getFormApiRef.current.setValues(formValues);

      // 延迟检查表单值是否设置成功
      setTimeout(() => {
        const currentValues = getFormApiRef.current.getValues();
        console.log("设置后的表单值:", currentValues);
        console.log("表单中的 checkMethod:", currentValues.checkMethod);
        console.log("表单中的 nfcNumber:", currentValues.nfcNumber);
      }, 100);
    }
  }, [unitEdit?.id, data, getFormApiRef]);

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft("unitEdit");
    getFormApiRef?.current?.reset?.();
    setUnitEdit({
      id: "",
      show: false,
    });
  }, [setUnitEdit, mutation, getFormApiRef]);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    getFormApiRef.current
      .validate()
      .then((values) => {
        if (unitEdit?.id) {
          mutation.mutate({
            id: unitEdit?.id,
            values: {
              ...values,
              safetySigns: values.safetySigns
                ? JSON.stringify(values.safetySigns)
                : null,
            },
          });
        } else {
          mutation.mutate({
            ...values,
            safetySigns: values.safetySigns
              ? JSON.stringify(values.safetySigns)
              : null,
          });
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };

  const title = unitEdit?.id ? "编辑风险单元" : "新增风险单元";
  const rules = [{ required: true, message: "此为必填项!" }];

  const handleSafetySigns = (signs) => {
    const customs = getFormApiRef.current
      .getValue("safetySigns")
      ?.filter((id) => `${id}`.includes("upload"));
    getFormApiRef.current.setValue("safetySigns", signs.concat(customs));
  };

  const handleCustomSafetySigns = (urls) => {
    const signs = getFormApiRef.current
      .getValue("safetySigns")
      .filter((id) => !`${id}`.includes("upload"));
    getFormApiRef.current.setValue("safetySigns", signs.concat(urls));
  };

  return (
    <>
      <DraftTrigger id="unitEdit" draftAtom={unitEditModalAtom} />
      <Modal
        title={title}
        visible={unitEdit?.show ?? false}
        width={800}
        keepDOM
        onCancel={handleClose}
        maskClosable={false}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              <Form.Section text={"关联风险对象"}>
                <Row gutter={20}>
                  <Col span={12}>
                    <ObjectSearch
                      field="riskObjectId"
                      label="所属风险对象"
                      association={
                        unitEdit?.id
                          ? []
                          : ["liableDepartmentId", "liablePersonId"]
                      }
                      placeholder=""
                      isRequired
                    />
                  </Col>
                  <Col span={12}>
                    <DepartmentSearch
                      field="liableDepartmentId"
                      label="责任部门"
                      placeholder=""
                      isRequired
                    />
                  </Col>
                  <Col span={12}>
                    <EmployeeSearch
                      field="liablePersonId"
                      label="责任人"
                      placeholder=""
                      isRequired
                    />
                  </Col>
                </Row>
              </Form.Section>

              <Form.Section text={"风险单元基础信息"}>
                <Row gutter={20}>
                  <Col span={24}>
                    <Form.Input
                      field="name"
                      label="风险单元名称"
                      trigger="blur"
                      rules={rules}
                    />
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={12}>
                    <Form.Select
                      field="checkMethod"
                      label="检查方式"
                      placeholder="请选择检查方式"
                      className="w-full"
                      rules={rules}
                    >
                      <Form.Select.Option value={CheckMethod.Normal}>
                        普通
                      </Form.Select.Option>
                      <Form.Select.Option value={CheckMethod.Scan}>
                        扫码
                      </Form.Select.Option>
                      <Form.Select.Option value={CheckMethod.Nfc}>
                        NFC
                      </Form.Select.Option>
                    </Form.Select>
                  </Col>
                  <Col span={12}>
                    {(() => {
                      console.log(
                        "渲染时 checkMethod:",
                        formState.values.checkMethod
                      );
                      console.log("CheckMethod.Nfc:", CheckMethod.Nfc);
                      console.log(
                        "条件判断结果:",
                        formState.values.checkMethod === CheckMethod.Nfc
                      );
                      console.log(
                        "当前 nfcNumber 值:",
                        formState.values.nfcNumber
                      );
                      return formState.values.checkMethod ===
                        CheckMethod.Nfc ? (
                        <Form.Input
                          field="nfcNumber"
                          label="NFC 编号"
                          trigger="blur"
                          rules={rules}
                          key="nfcNumber" // 添加 key 确保重新渲染
                        />
                      ) : null;
                    })()}
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Input label="二维码" field="qrcode" trigger="blur" />
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Select
                      label="是否允许批量提交"
                      field="allowBatchSubmit"
                      className="w-full"
                      rules={rules}
                    >
                      {ALLOW_ALLOWNOT_MAP.map((item) => (
                        <Form.Select.Option key={item.id} value={item.id}>
                          {item.name}
                        </Form.Select.Option>
                      ))}
                    </Form.Select>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.TextArea
                      field="riskAnalysis"
                      label="危害分析"
                      rules={rules}
                    />
                  </Col>
                  <Col span={24}>
                    <Form.TextArea
                      field="riskMajorResult"
                      label="主要后果"
                      rules={rules}
                    />
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={24} className="safety-signs-col">
                    <SafetySigns
                      defaultValue={formState.values.safetySigns || []}
                      onOk={handleSafetySigns}
                      field="safetySigns"
                      label="安全警示标志"
                      placeholder="选择国标标志"
                      suffix={
                        <Upload
                          listType="list"
                          showUploadList={false}
                          onSuccess={handleCustomSafetySigns}
                          field="safetySigns_upload"
                          formField="safetySigns"
                          label=" "
                          multiple
                        >
                          <buttom className="btn btn-accent rounded btn-sm">
                            自定义上传
                          </buttom>
                        </Upload>
                      }
                    />
                    <Form.CheckboxGroup
                      field="safetySigns"
                      label={" "}
                      type="card"
                    >
                      <div className="flex justify-start items-start flex-wrap gap-3 ml-[70px] unit-safety-signs">
                        {(formState.values.safetySigns || []).map((id) => {
                          let src;
                          if (!isNaN(+id)) {
                            // 兼容旧数据
                            const item = SAFETY_SIGNS_MAP.find(
                              (item) => item.id === id
                            );
                            if (item) {
                              src = `/static/signs/${item.id}_${item.name}.${item.ext}`;
                            }
                          }
                          return (
                            <Form.Checkbox value={id} style={{ width: 100 }}>
                              <Avatar
                                style={{
                                  display: "block",
                                  width: "fit-content",
                                  height: "fit-content",
                                }}
                                shape="square"
                                src={`${base_url}${src || id}`}
                              />
                            </Form.Checkbox>
                          );
                        })}
                      </div>
                    </Form.CheckboxGroup>
                  </Col>
                </Row>
              </Form.Section>
              {unitEdit?.id ? null : (
                <Draft id="unitEdit" draftAtom={unitEditModalAtom} />
              )}
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
