import { Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import { useAuth } from "@reactivers/hooks";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { postDeadlineRectification } from "api/doubleGuard";
import {
  DangerModalType,
  dangerEditModalAtom,
  dangerFnAtom,
} from "atoms/doubleGuard";
import {
  DANGER_SOURCE_MAP,
  EmployeeSearch,
  ObjectSearch,
  RestSelect,
  SNAP_DANGER_CATEGORY_MAP,
  SNAP_DANGER_TYPE_MAP,
  SNAP_LEVEL_MAP,
  SNAP_SELF_EVALUATION_MAP,
  SNAP_SOURCE_MAP,
  Upload,
} from "components";
import { Draft, DraftTrigger, destroyDraft } from "components/Draft";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { isEmpty, isNil } from "ramda";
import { FC, useCallback, useRef } from "react";
import { formatRFC3339 } from "utils";

type CreateDelayedProps = {
  callback?: (id: number) => void;
  internalSourceDisabled?: boolean;
  initValues?: any;
  api?: any;
};

export const CreateDelayed: FC<CreateDelayedProps> = ({
  callback,
  internalSourceDisabled = false,
  initValues = {},
  api = postDeadlineRectification,
}) => {
  const queryClient = useQueryClient();
  const getFormApiRef = useRef<any>(null);
  const [dangerModal, setDangerModal] = useAtom(dangerEditModalAtom);
  const reset = useResetAtom(dangerEditModalAtom);
  const [dangerFn] = useAtom(dangerFnAtom);
  const { user } = useAuth();

  const mutation = useMutation({
    mutationFn: api,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({ queryKey: ["getDangerList"] });
        dangerFn?.refetch?.();
        destroyDraft("CreateDelayedDangerModal");
        reset();
        callback?.(res?.data?.id);
      }
    },
  });

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    destroyDraft("CreateDelayedDangerModal");
    getFormApiRef?.current?.reset?.();
    reset();
  }, [reset, mutation, getFormApiRef]);

  const handleSetFormApi = useCallback(
    (formApi) => {
      getFormApiRef.current = formApi;
    },
    [getFormApiRef]
  );

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    const queryParams = dangerModal?.queryParams;
    getFormApiRef.current
      .validate()
      .then(async (values) => {
        const obj = {
          name: values.name,
          riskObjectId: values.riskObjectId,
          reporterIds: values?.reporterIds?.length
            ? JSON.stringify(values.reporterIds)
            : null,
          reportTime: values?.reportTime
            ? formatRFC3339(values?.reportTime)
            : null,
          source: values.source,
          dangerType: values.dangerType,
          dangerCategory: values.dangerCategory,
          description: values.description,
          reportPictures: values.reportPictures,
          selfEvaluation: values.selfEvaluation,
          evaluatorCandIds: values?.evaluatorCands?.length
            ? JSON.stringify(values.evaluatorCands)
            : null,
          rectifierCandIds: values?.rectifierCands?.length
            ? JSON.stringify(values.rectifierCands)
            : null,
          deadline: values?.deadline ? formatRFC3339(values?.deadline) : null,
          level: values.level,
        };
        if (isNil(queryParams) || isEmpty(queryParams)) {
          mutation.mutate(obj);
        } else {
          mutation.mutate({ ...queryParams, values: { ...obj } });
        }
      })
      .catch((errors) => {
        console.log(errors);
        const [formFieldId] = Object.keys(errors || {});
        if (formFieldId) {
          document.getElementById(formFieldId)?.scrollIntoViewIfNeeded?.();
        }
      });
  };
  const title = "生成限期整改隐患";
  const rules = [{ required: true, message: "此为必填项!" }];

  return (
    <>
      <DraftTrigger
        id="CreateDelayedDangerModal"
        draftAtom={dangerEditModalAtom}
      />
      <Modal
        title={title}
        visible={
          (dangerModal?.show ?? false) &&
          dangerModal.type === DangerModalType.Delayed
        }
        width={800}
        keepDOM
        onCancel={handleClose}
        maskClosable={false}
        footer={
          <div className="flex gap-2 justify-end">
            <button className="btn rounded btn-sm" onClick={handleClose}>
              取消
            </button>
            {mutation.isLoading ? (
              <button className="btn rounded btn-primary btn-sm btn-disabled">
                <span className="loading loading-spinner loading-xs"></span>
                确定
              </button>
            ) : (
              <button
                className="btn rounded btn-primary btn-sm"
                onClick={handleOk}
              >
                确定
              </button>
            )}
          </div>
        }
        centered
      >
        <Form
          labelPosition="left"
          labelAlign="right"
          labelWidth={100}
          getFormApi={handleSetFormApi}
        >
          {({ formState }) => (
            <>
              <Form.Section text="隐患信息">
                <Row gutter={20}>
                  <Col span={12}>
                    <Form.Input
                      field="name"
                      label="隐患名称"
                      placeholder="请填写隐患名称"
                      rules={rules}
                    />
                  </Col>
                  <Col span={12}>
                    <ObjectSearch
                      field="riskObjectId"
                      label="分析对象"
                      placeholder="请选择分析对象"
                      isRequired
                    />
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={12}>
                    <EmployeeSearch
                      field="reporterIds"
                      label="登记人员"
                      placeholder="请选择登记人员"
                      multiple
                      isRequired
                      initValue={(
                        formState.values.reporterIds ?? [user.userInfo]
                      ).map((i) => i?.id)}
                    />
                  </Col>
                  <Col span={12}>
                    <Form.DatePicker
                      field="reportTime"
                      type="dateTime"
                      label="登记时间"
                      className="w-full"
                      initValue={new Date()}
                    />
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={12}>
                    <RestSelect
                      field="source"
                      label="隐患来源"
                      placeholder="请选择隐患来源"
                      options={SNAP_SOURCE_MAP}
                      isRequired
                      rules={rules}
                    />
                  </Col>
                  <Col span={12}>
                    <RestSelect
                      field="dangerType"
                      label="隐患类型"
                      placeholder="请选择隐患类型"
                      options={SNAP_DANGER_TYPE_MAP}
                      isRequired
                      rules={rules}
                    />
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={12}>
                    <RestSelect
                      field="dangerCategory"
                      label="隐患类别"
                      placeholder="请选择隐患类别"
                      options={SNAP_DANGER_CATEGORY_MAP}
                      isRequired
                      rules={rules}
                    />
                  </Col>
                  <Col span={12}></Col>
                </Row>
                <Form.TextArea
                  field="description"
                  placeholder="请填写隐患描述"
                  label="隐患描述"
                  rules={rules}
                />
                <Upload
                  field="reportPictures_upload"
                  formField="reportPictures"
                  label="隐患图片"
                  multiple
                />
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Select
                      label="隐患出处"
                      field="internalSource"
                      className="w-full"
                      rules={rules}
                      initValue={initValues.internalSource}
                      disabled={internalSourceDisabled}
                    >
                      {DANGER_SOURCE_MAP.map((item) => (
                        <Form.Select.Option key={item.id} value={item.id}>
                          {item.name}
                        </Form.Select.Option>
                      ))}
                    </Form.Select>
                  </Col>
                </Row>
              </Form.Section>

              <Form.Section text="隐患处理">
                <RestSelect
                  field="selfEvaluation"
                  label="是否自评"
                  placeholder="请选择隐患类别"
                  options={SNAP_SELF_EVALUATION_MAP}
                  isRequired
                  rules={rules}
                />
                {formState.values.selfEvaluation == 2 ? (
                  <EmployeeSearch
                    field="evaluatorCands"
                    label="评估人列表"
                    placeholder="请选择评估人"
                    multiple
                    isRequired
                    initValue={(formState.values.evaluatorCands ?? []).map(
                      (i) => i?.id
                    )}
                  />
                ) : null}
                {formState.values.selfEvaluation == 1 ? (
                  <>
                    <EmployeeSearch
                      field="rectifierCands"
                      label="整改人员"
                      placeholder="请选择整改人员"
                      multiple
                      isRequired
                      initValue={(formState.values.acceptorCands ?? []).map(
                        (i) => i?.id
                      )}
                    />
                    <Form.DatePicker
                      field="deadline"
                      type="dateTime"
                      label="整改期限"
                      className="w-full"
                      isRequired
                      rules={rules}
                    />
                    <RestSelect
                      className="w-full"
                      field="level"
                      label="隐患等级"
                      placeholder="请选择隐患等级"
                      options={SNAP_LEVEL_MAP}
                      isRequired
                      rules={rules}
                    />
                    <Form.TextArea
                      field="evaluateComment"
                      label="评估意见"
                      className="w-full"
                    />
                  </>
                ) : null}
              </Form.Section>
              <Draft
                id="CreateDelayedDangerModal"
                draftAtom={dangerEditModalAtom}
              />
            </>
          )}

          {/* <ComponentUsingFormApi /> */}
        </Form>
      </Modal>
    </>
  );
};
