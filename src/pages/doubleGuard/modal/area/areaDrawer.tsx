import { areaDrawerModalAtom, areaFnAtom } from "@atoms/doubleGuard";
import { Modal, Toast } from "@douyinfe/semi-ui";
import { useMutation, useQuery } from "@tanstack/react-query";
import { drawArea, getDrawAreas } from "api/doubleGuard";
import * as Cesium from "cesium";
import "cesium/Build/Cesium/Widgets/widgets.css";
import { RISK_LEVEL_COLOR_MAP } from "components";
import {
  cesiumInit,
  cesiumSceneLightOptions,
  cesiumViewerOptions,
} from "hooks/cesiumInit";
import { useAtom } from "jotai";
import { useCallback, useEffect, useRef, useState } from "react";
import CesiumEntityDraw from "utils/CesiumEntityDraw";

/**
 * 模型地图分区绘制组件
 *
 * See also {@link AreaMapPicker} {@link AreaMapMarker} {@link AreaMapPolygon}
 *
 * See also {@link MapPicker}
 *
 * See also {@link AreaPage}
 *
 * @example
 * ```typescript
 * <AreaDrawer />
 * ```
 */
export const AreaDrawer = () => {
  const [areaDrawer, setAreaDrawer] = useAtom(areaDrawerModalAtom);
  const [areaFn] = useAtom(areaFnAtom);

  const id = `cesium-${areaDrawer?.id}`;

  const { isLoading, data } = useQuery({
    queryKey: [id],
    queryFn: getDrawAreas,
    enabled: !!areaDrawer?.id,
  });
  const mutation = useMutation({
    mutationFn: drawArea,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        areaFn?.refetch?.();
        setAreaDrawer({
          id: "",
          show: false,
        });
      }
    },
  });

  const [alerted, setAlerted] = useState(false);
  const [overflowBytesTimes, setOverflowBytesTimes] = useState(1);
  const cesiumRef = useRef();
  const viewerRef = useRef();
  const entities = useRef({});
  const drawer = useRef();
  const [drawing, toggleDrawing] = useState(false);

  const drawAreaOverlay = useCallback((entity) => {
    const color = RISK_LEVEL_COLOR_MAP.find(
      (risk) => risk.id === (entity.riskLevel || 5)
    ).color;
    entities.current[entity.id] = new Cesium.Entity({
      id: `risk-overlay-${entity.id}`,
      polygon: {
        hierarchy: Cesium.Cartesian3.fromDegreesArray(
          entity.points
            .map((point) => [point.longitude, point.latitude])
            .flat(2)
          // [
          //   [121.46602858807483, 28.678413749226987],
          //   [121.46652138917841, 28.678195952724426],
          //   [121.46658834937118, 28.678319518912755],
          //   [121.46608168435698, 28.67853537691449],
          // ].flat(2)
        ),
        material: Cesium.Color.fromCssColorString(color).withAlpha(0.6),
      },
    });
    viewerRef.current.entities.add(entities.current[entity.id]);
  }, []);

  const tilesetRef = useRef();

  const initializeCesium = useCallback(async () => {
    if (!data?.data?.cesiumUriPrefix) {
      if (cesiumRef.current) {
        cesiumRef.current.innerText = "Cesium 地址 uri prefix 不可用";
      }
      return;
    }
    cesiumRef.current.innerText = "";
    // Cesium.Ion.defaultAccessToken = import.meta.env.VITE_CESIUM_TOKEN;
    console.log(data.data.cesiumToken);
    Cesium.Ion.defaultAccessToken = data.data.cesiumToken;

    const viewer = new Cesium.Viewer(id, cesiumViewerOptions);
    viewerRef.current = viewer;
    try {
      viewer._cesiumWidget._creditContainer.parentNode.removeChild(
        viewer._cesiumWidget._creditContainer
      );
      viewer.scene.light = new Cesium.DirectionalLight(cesiumSceneLightOptions);

      const tileset = await cesiumInit(data, viewer);

      viewer.scene.primitives.add(tileset);
      tilesetRef.current = tileset;
      await viewer.zoomTo(tileset);

      data?.data?.areas?.forEach?.(drawAreaOverlay);
      localStorage.setItem("risk", JSON.stringify(data?.data?.areas));
      console.log(data);
    } catch (error) {
      console.log(`Error loading tileset: ${error}`);
      if (error?.message?.includes("maximumCacheOverflowBytes")) {
        setOverflowBytesTimes((_) => _ + 1);
      }
    }
  }, [id, overflowBytesTimes, data?.data, drawAreaOverlay]);

  useEffect(() => {
    if (areaDrawer.show && data?.data?.cesiumUriPrefix) {
      setTimeout(initializeCesium);
    }
    return () => {
      if (!areaDrawer.show) {
        if (viewerRef.current) {
          viewerRef.current.destroy();
          viewerRef.current = null;
        }
        if (cesiumRef.current) {
          cesiumRef.current.innerHTML = "";
        }
      }
    };
  }, [id, initializeCesium, areaDrawer.show]);

  const handleClose = useCallback(() => {
    if (mutation.isLoading) {
      return;
    }
    if (viewerRef.current) {
      viewerRef.current.destroy();
      viewerRef.current = null;
    }
    if (cesiumRef.current) {
      cesiumRef.current.innerHTML = "";
    }
    if (drawer.current) {
      drawer.current.clear();
      drawer.current = null;
    }
    if (drawing) {
      toggleDrawing(false);
    }
    setAlerted(false);
    setAreaDrawer({
      id: "",
      show: false,
    });
  }, [setAreaDrawer, mutation]);

  const onStopDraw = useCallback(
    (points) => {
      toggleDrawing(false);
      if (drawer.current) {
        drawer.current.clear();
      }
      points = points.map(([longitude, latitude]) => ({ longitude, latitude }));
      drawAreaOverlay({
        id: areaDrawer.id,
        riskLevel: areaDrawer.riskLevel,
        points,
      });
      entities.current[`draft-${areaDrawer.id}`] = points;
    },
    [areaDrawer, drawAreaOverlay]
  );
  const onStartDraw = useCallback(() => {
    toggleDrawing(true);
    if (entities.current[areaDrawer.id]) {
      viewerRef.current.entities.removeById(`risk-overlay-${areaDrawer.id}`);
      delete entities.current[areaDrawer.id];
    }
    if (drawer.current) {
      drawer.current.clear();
      drawer.current = null;
    }
    const color = RISK_LEVEL_COLOR_MAP.find(
      (risk) => risk.id === (areaDrawer.riskLevel || 5)
    ).color;
    drawer.current = new CesiumEntityDraw(viewerRef.current, {
      material: Cesium.Color.fromCssColorString(color).withAlpha(0.6),
    });
    drawer.current.drawPolygon(onStopDraw);
  }, [onStopDraw, areaDrawer]);
  const onClearDraw = () => {
    if (drawer.current) {
      drawer.current.clear();
    }
    viewerRef.current.entities.removeById(`risk-overlay-${areaDrawer.id}`);
    delete entities.current[areaDrawer.id];
  };

  const handleOk = () => {
    if (mutation.isLoading) {
      return;
    }
    if (entities.current[`draft-${areaDrawer.id}`].length < 3) {
      return;
    }
    mutation.mutate({
      id: areaDrawer.id,
      body: entities.current[`draft-${areaDrawer.id}`],
    });
  };

  const [othersVisible, setOthersVisible] = useState(true);
  const toggleVisible = useCallback(() => {
    setOthersVisible(!othersVisible);
    data?.data?.areas?.forEach(({ id }) => {
      if (id !== areaDrawer.id) {
        if (entities.current[id]) {
          entities.current[id].show = !othersVisible;
        }
      }
    });
  }, [othersVisible, data?.data?.areas, areaDrawer?.id]);

  useEffect(() => {
    if (areaDrawer.show && !alerted) {
      setAlerted(true);
      Modal.info({
        centered: true,
        title: " ",
        content: (
          <>
            <p>单击鼠标左键确定标注点，单击鼠标右键结束绘制。</p>
            <p>
              标注风险分区区域的标注点时，标注点 (三个及以上)
              所围区域即为该风险分区区域，绘制过程中会显示。
            </p>
            <p>
              注意:
              标注点时按同一方向（顺时针/逆时针）标注，否则标点所围区域会发生错乱!
            </p>
          </>
        ),
        onCancel: handleClose,
        okButtonProps: {
          className: "btn rounded btn-primary btn-sm",
        },
        maskClosable: false,
      });
    }
  }, [areaDrawer.show, handleClose]);

  const goHome = () => {
    viewerRef.current.zoomTo(tilesetRef.current);
  };

  const alreadyDrawed =
    areaDrawer?.id &&
    data?.data?.areas?.some(({ id }) => id === areaDrawer?.id);

  return (
    <>
      <Modal
        title="绘制风险分区"
        width="fit-content"
        height="fit-content"
        bodyStyle={{
          width: "fit-content",
          height: "fit-content",
          paddingBottom: 20,
        }}
        visible={areaDrawer?.show ?? false}
        onCancel={handleClose}
        maskClosable={false}
        footer={null}
        centered
      >
        <div className="flex gap-3 mb-[20px] items-center">
          <button
            className="btn rounded btn-primary btn-sm"
            disabled={drawing || mutation.isLoading}
            onClick={onStartDraw}
          >
            {drawing ? "绘制中" : alreadyDrawed ? "重新绘制" : "开始绘制"}
          </button>
          {!drawing ? (
            <button
              className="btn rounded btn-neutral btn-sm"
              disabled={mutation.isLoading}
              onClick={onClearDraw}
            >
              重置标记
            </button>
          ) : null}
          <button
            className="btn rounded btn-primary btn-sm"
            disabled={mutation.isLoading}
            onClick={handleOk}
          >
            {mutation.isLoading ? (
              <span className="loading loading-spinner loading-xs"></span>
            ) : (
              "确认提交"
            )}
          </button>
          <button
            className="btn rounded btn-accent btn-sm"
            onClick={toggleVisible}
          >
            {othersVisible ? "隐藏" : "显示"}其他区域
          </button>
          <button className="btn rounded btn-accent btn-sm" onClick={goHome}>
            重置镜头
          </button>
        </div>
        {drawing ? (
          <p className="mb-[20px]">
            单击鼠标左键确定标注点，单击鼠标右键结束绘制。按住Ctrl键后，单击鼠标左键拖动渲染镜头。
          </p>
        ) : (
          ""
        )}
        <div
          ref={cesiumRef}
          className="w-[80vw] h-[70vh] min-w-[800px] min-h-[500px]"
          id={id}
        ></div>
      </Modal>
    </>
  );
};
